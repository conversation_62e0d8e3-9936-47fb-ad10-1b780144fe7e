"""
Demo script để test GUI với mock data
"""

import tkinter as tk
from tkinter import ttk
from gui_interface import Domain<PERSON>heckerG<PERSON>
from namecheap_api import DomainCheckResult
import threading
import time


class MockAPI:
    """Mock API để test GUI"""
    
    def check_domains(self, domains):
        """Mock domain check với delay để test progress"""
        time.sleep(2)  # Simulate API delay
        
        results = []
        for i, domain in enumerate(domains):
            if i % 3 == 0:  # Available domain
                result = DomainCheckResult(
                    domain=domain,
                    available=True,
                    is_premium=False,
                    error_no=0
                )
            elif i % 3 == 1:  # Premium domain
                result = DomainCheckResult(
                    domain=domain,
                    available=True,
                    is_premium=True,
                    registration_price=1000.0,
                    renewal_price=1000.0,
                    currency="USD",
                    error_no=0
                )
            else:  # Unavailable domain
                result = DomainCheckResult(
                    domain=domain,
                    available=False,
                    is_premium=False,
                    error_no=0
                )
            results.append(result)
        
        return results
    
    def get_pricing(self, tld, action):
        """Mock pricing API"""
        time.sleep(0.5)  # Simulate API delay
        
        # Mock pricing data
        pricing_map = {
            'com': {'price': 12.99, 'currency': 'USD'},
            'net': {'price': 14.99, 'currency': 'USD'},
            'org': {'price': 13.99, 'currency': 'USD'},
            'info': {'price': 19.99, 'currency': 'USD'},
            'xyz': {'price': 2.99, 'currency': 'USD'},
        }
        
        from namecheap_api import PricingInfo
        
        if tld in pricing_map:
            price_data = pricing_map[tld]
            return [PricingInfo(
                tld=tld,
                duration=1,
                duration_type="YEAR",
                price=price_data['price'],
                regular_price=price_data['price'],
                your_price=price_data['price'],
                coupon_price=0,
                currency=price_data['currency']
            )]
        
        return []


def demo_gui():
    """Demo GUI với mock data"""
    print("🎮 Starting GUI Demo with Mock Data...")
    
    # Tạo GUI instance
    app = DomainCheckerGUI()
    
    # Replace API với mock
    app.api = MockAPI()
    
    # Pre-fill với sample domains
    sample_domains = """# Demo domains
example-test-123.com
premium-domain-456.net
unavailable-domain-789.org
awesome-site-2024.info
my-cool-domain.xyz"""
    
    app.domain_text.delete(1.0, tk.END)
    app.domain_text.insert(1.0, sample_domains)
    app.domain_text.config(foreground='black')
    app.placeholder_active = False
    
    # Update status
    app.status_var.set("🎮 Demo mode - Sử dụng mock data")
    
    print("✅ GUI Demo ready!")
    print("💡 Tip: Click 'Kiểm tra Domain' để test với mock data")
    
    # Run GUI
    app.run()


if __name__ == "__main__":
    demo_gui()
