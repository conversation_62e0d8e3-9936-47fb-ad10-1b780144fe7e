"""
CLI Interface cho Domain Checker
Giao diện dòng lệnh đơn giản
"""

import sys
import os
from typing import List
from namecheap_api import NamecheapAPI, DomainCheckResult, NamecheapAPIError
from error_handler import ErrorHandler, handle_api_error


class DomainCheckerCLI:
    """CLI cho Domain Checker"""
    
    def __init__(self):
        self.api = None
        self.load_api_config()
    
    def load_api_config(self):
        """Tải cấu hình API"""
        try:
            self.api = NamecheapAPI()
            print("✅ API đã sẵn sàng")
        except Exception as e:
            print(f"❌ Lỗi cấu hình API: {str(e)}")
            print("Vui lòng kiểm tra file config.ini")
            sys.exit(1)
    
    def print_banner(self):
        """In banner ứng dụng"""
        print("=" * 60)
        print("🌐 NAMECHEAP DOMAIN AVAILABILITY CHECKER")
        print("=" * 60)
        print()
    
    def get_domains_from_input(self) -> List[str]:
        """<PERSON><PERSON><PERSON> danh sách domain từ input của người dùng"""
        print("Nhập danh sách domain (mỗi domain một dòng).")
        print("Nhập 'done' hoặc để trống để kết thúc:")
        print()
        
        domains = []
        while True:
            try:
                domain = input("Domain: ").strip()
                if not domain or domain.lower() == 'done':
                    break
                if domain.startswith('#'):  # Bỏ qua comment
                    continue
                domains.append(domain)
            except KeyboardInterrupt:
                print("\n\n👋 Tạm biệt!")
                sys.exit(0)
        
        return domains
    
    def get_domains_from_file(self, filename: str) -> List[str]:
        """Lấy danh sách domain từ file"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                domains = []
                for line in f:
                    domain = line.strip()
                    if domain and not domain.startswith('#'):
                        domains.append(domain)
                return domains
        except FileNotFoundError:
            print(f"❌ Không tìm thấy file: {filename}")
            return []
        except Exception as e:
            print(f"❌ Lỗi đọc file: {str(e)}")
            return []
    
    def format_result(self, result: DomainCheckResult) -> str:
        """Format kết quả kiểm tra domain"""
        if result.error_no != 0:
            error_msg = ErrorHandler.get_error_message(result.error_no)
            return f"❌ {result.domain}: Lỗi - {error_msg}"
        
        if result.available:
            status = "✅ CÓ THỂ MUA"
            domain_type = " (PREMIUM)" if result.is_premium else " (THƯỜNG)"
            
            price_info = ""
            if result.is_premium and result.registration_price > 0:
                price_info = f" - Giá: ${result.registration_price:.2f}"
                if result.renewal_price > 0:
                    price_info += f" (gia hạn: ${result.renewal_price:.2f})"
            
            return f"{status} {result.domain}{domain_type}{price_info}"
        else:
            return f"❌ KHÔNG KHẢ DỤNG {result.domain}"
    
    def print_results(self, results: List[DomainCheckResult]):
        """In kết quả kiểm tra"""
        print("\n" + "=" * 60)
        print("📊 KẾT QUẢ KIỂM TRA")
        print("=" * 60)
        
        available_count = 0
        unavailable_count = 0
        error_count = 0
        premium_count = 0
        
        for result in results:
            print(self.format_result(result))
            
            if result.error_no != 0:
                error_count += 1
            elif result.available:
                available_count += 1
                if result.is_premium:
                    premium_count += 1
            else:
                unavailable_count += 1
        
        # Thống kê
        print("\n" + "-" * 60)
        print("📈 THỐNG KÊ:")
        print(f"   Tổng số domain: {len(results)}")
        print(f"   Có thể mua: {available_count}")
        print(f"   Không khả dụng: {unavailable_count}")
        print(f"   Lỗi: {error_count}")
        print(f"   Premium domains: {premium_count}")
        print("-" * 60)
    
    def save_results_to_file(self, results: List[DomainCheckResult], filename: str):
        """Lưu kết quả ra file"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("Domain\tTrạng thái\tLoại\tGiá đăng ký\tGiá gia hạn\tTiền tệ\tGhi chú\n")
                
                for result in results:
                    if result.error_no != 0:
                        error_msg = ErrorHandler.get_error_message(result.error_no)
                        f.write(f"{result.domain}\tLỗi\tN/A\tN/A\tN/A\tN/A\t{error_msg}\n")
                    else:
                        status = "Có thể mua" if result.available else "Không khả dụng"
                        domain_type = "Premium" if result.is_premium else "Thường"
                        reg_price = f"{result.registration_price:.2f}" if result.registration_price > 0 else "N/A"
                        renew_price = f"{result.renewal_price:.2f}" if result.renewal_price > 0 else "N/A"
                        currency = result.currency if result.currency else "USD"
                        note = result.description if result.description else ""
                        
                        f.write(f"{result.domain}\t{status}\t{domain_type}\t{reg_price}\t{renew_price}\t{currency}\t{note}\n")
            
            print(f"✅ Đã lưu kết quả vào: {filename}")
            
        except Exception as e:
            print(f"❌ Lỗi khi lưu file: {str(e)}")
    
    @handle_api_error
    def check_domains(self, domains: List[str]) -> List[DomainCheckResult]:
        """Kiểm tra danh sách domain"""
        if not domains:
            print("❌ Không có domain nào để kiểm tra")
            return []
        
        print(f"\n🔍 Đang kiểm tra {len(domains)} domain...")
        print("Vui lòng đợi...")
        
        try:
            results = self.api.check_domains(domains)
            return results
        except NamecheapAPIError as e:
            # Error đã được handle bởi decorator
            return []
    
    def interactive_mode(self):
        """Chế độ tương tác"""
        self.print_banner()
        
        while True:
            print("\n🎯 CHỌN CHỨC NĂNG:")
            print("1. Nhập domain từ bàn phím")
            print("2. Đọc domain từ file")
            print("3. Thoát")
            
            try:
                choice = input("\nLựa chọn (1-3): ").strip()
                
                if choice == '1':
                    domains = self.get_domains_from_input()
                    if domains:
                        results = self.check_domains(domains)
                        if results:
                            self.print_results(results)
                            
                            # Hỏi có muốn lưu kết quả không
                            save = input("\n💾 Lưu kết quả ra file? (y/n): ").strip().lower()
                            if save in ['y', 'yes', 'có']:
                                filename = input("Tên file (mặc định: results.txt): ").strip()
                                if not filename:
                                    filename = "results.txt"
                                self.save_results_to_file(results, filename)
                    else:
                        print("❌ Không có domain nào để kiểm tra")
                
                elif choice == '2':
                    filename = input("Đường dẫn file: ").strip()
                    if filename:
                        domains = self.get_domains_from_file(filename)
                        if domains:
                            print(f"📁 Đã đọc {len(domains)} domain từ file")
                            results = self.check_domains(domains)
                            if results:
                                self.print_results(results)
                                
                                # Tự động lưu kết quả
                                output_file = filename.replace('.txt', '_results.txt')
                                self.save_results_to_file(results, output_file)
                        else:
                            print("❌ Không thể đọc domain từ file")
                
                elif choice == '3':
                    print("\n👋 Tạm biệt!")
                    break
                
                else:
                    print("❌ Lựa chọn không hợp lệ")
                    
            except KeyboardInterrupt:
                print("\n\n👋 Tạm biệt!")
                break
            except Exception as e:
                print(f"❌ Lỗi: {str(e)}")
    
    def batch_mode(self, input_file: str, output_file: str = None):
        """Chế độ batch processing"""
        print(f"📁 Đọc domain từ file: {input_file}")
        
        domains = self.get_domains_from_file(input_file)
        if not domains:
            return
        
        print(f"📊 Tìm thấy {len(domains)} domain")
        results = self.check_domains(domains)
        
        if results:
            self.print_results(results)
            
            if not output_file:
                output_file = input_file.replace('.txt', '_results.txt')
            
            self.save_results_to_file(results, output_file)


def main():
    """Hàm main"""
    cli = DomainCheckerCLI()
    
    if len(sys.argv) > 1:
        # Batch mode
        input_file = sys.argv[1]
        output_file = sys.argv[2] if len(sys.argv) > 2 else None
        cli.batch_mode(input_file, output_file)
    else:
        # Interactive mode
        cli.interactive_mode()


if __name__ == "__main__":
    main()
