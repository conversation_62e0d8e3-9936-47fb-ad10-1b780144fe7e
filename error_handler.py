"""
Error Handler cho Namecheap API
Module xử lý và hiển thị lỗi một cách thân thiện với người dùng
"""

from typing import Dict, Optional
from namecheap_api import NamecheapAPIError


class ErrorHandler:
    """Class xử lý lỗi từ Namecheap API"""
    
    # Mapping error codes với thông báo tiếng Việt
    ERROR_MESSAGES = {
        # Global Error Codes
        1010101: "Thiếu tham số APIUser",
        1030408: "Loại xác thực không được hỗ trợ",
        1010104: "Thiếu tham số Command",
        1010102: "Thiếu tham số APIKey",
        1011102: "Thiếu tham số APIKey",
        1010105: "Thiếu tham số ClientIP",
        1011105: "Thiếu tham số ClientIP",
        1050900: "Lỗi không xác định khi xác thực APIUser",
        1011150: "Tham số RequestIP không hợp lệ",
        1017150: "Tham số RequestIP bị vô hiệu hóa hoặc khóa",
        1017105: "Tham số ClientIP bị vô hiệu hóa hoặc khóa",
        1017101: "Tham số ApiUser bị vô hiệu hóa hoặc khóa",
        1017410: "Quá nhiều thanh toán bị từ chối",
        1017411: "Quá nhiều lần đăng nhập thất bại",
        1019103: "Tham số UserName không khả dụng",
        1016103: "Tham số UserName không được ủy quyền",
        1017103: "Tham số UserName bị vô hiệu hóa hoặc khóa",
        
        # Domain Check Error Codes
        3031510: "Lỗi phản hồi từ Enom khi số lỗi != 0",
        3011511: "Phản hồi không xác định từ nhà cung cấp",
        2011169: "Chỉ cho phép tối đa 50 domain trong một lần kiểm tra",
        
        # Pricing Error Codes
        2011170: "Mã khuyến mãi không hợp lệ",
        2011298: "Loại sản phẩm không hợp lệ",
        
        # Network và Connection Errors
        0: "Lỗi kết nối mạng hoặc lỗi không xác định"
    }
    
    # Mapping error codes với giải pháp
    ERROR_SOLUTIONS = {
        1010101: "Vui lòng kiểm tra và cập nhật APIUser trong file config.ini",
        1010102: "Vui lòng kiểm tra và cập nhật APIKey trong file config.ini",
        1011102: "Vui lòng kiểm tra và cập nhật APIKey trong file config.ini",
        1010105: "Vui lòng kiểm tra và cập nhật ClientIP trong file config.ini",
        1011105: "Vui lòng kiểm tra và cập nhật ClientIP trong file config.ini",
        1017105: "IP của bạn có thể bị chặn. Liên hệ Namecheap để whitelist IP",
        1017101: "Tài khoản API của bạn có thể bị khóa. Liên hệ Namecheap support",
        1017410: "Tài khoản có vấn đề về thanh toán. Kiểm tra billing information",
        1017411: "Tài khoản bị khóa do quá nhiều lần đăng nhập sai. Đợi hoặc liên hệ support",
        1019103: "Username không tồn tại. Kiểm tra lại username trong config",
        1016103: "Username không có quyền truy cập API. Kiểm tra API permissions",
        1017103: "Username bị khóa. Liên hệ Namecheap support",
        2011169: "Giảm số lượng domain xuống dưới 50 domain mỗi lần kiểm tra",
        2011170: "Kiểm tra lại mã khuyến mãi hoặc bỏ qua mã này",
        2011298: "Kiểm tra lại loại sản phẩm trong request",
        0: "Kiểm tra kết nối internet và thử lại sau"
    }
    
    # Phân loại mức độ nghiêm trọng của lỗi
    ERROR_SEVERITY = {
        "CRITICAL": [1017101, 1017103, 1017410, 1017411],  # Lỗi tài khoản nghiêm trọng
        "HIGH": [1010101, 1010102, 1011102, 1010105, 1011105, 1017105],  # Lỗi cấu hình
        "MEDIUM": [1019103, 1016103, 2011169, 2011298],  # Lỗi tham số
        "LOW": [2011170, 3031510, 3011511],  # Lỗi nhỏ
        "NETWORK": [0]  # Lỗi mạng
    }
    
    @classmethod
    def get_error_message(cls, error_code: int) -> str:
        """
        Lấy thông báo lỗi bằng tiếng Việt
        
        Args:
            error_code: Mã lỗi từ Namecheap API
            
        Returns:
            Thông báo lỗi bằng tiếng Việt
        """
        return cls.ERROR_MESSAGES.get(error_code, f"Lỗi không xác định (Mã: {error_code})")
    
    @classmethod
    def get_error_solution(cls, error_code: int) -> str:
        """
        Lấy giải pháp cho lỗi
        
        Args:
            error_code: Mã lỗi từ Namecheap API
            
        Returns:
            Giải pháp khắc phục lỗi
        """
        return cls.ERROR_SOLUTIONS.get(error_code, "Liên hệ Namecheap support để được hỗ trợ")
    
    @classmethod
    def get_error_severity(cls, error_code: int) -> str:
        """
        Lấy mức độ nghiêm trọng của lỗi
        
        Args:
            error_code: Mã lỗi từ Namecheap API
            
        Returns:
            Mức độ nghiêm trọng: CRITICAL, HIGH, MEDIUM, LOW, NETWORK
        """
        for severity, codes in cls.ERROR_SEVERITY.items():
            if error_code in codes:
                return severity
        return "UNKNOWN"
    
    @classmethod
    def format_error_message(cls, error: NamecheapAPIError, include_solution: bool = True) -> str:
        """
        Format thông báo lỗi đầy đủ
        
        Args:
            error: NamecheapAPIError object
            include_solution: Có bao gồm giải pháp không
            
        Returns:
            Thông báo lỗi được format
        """
        message = cls.get_error_message(error.error_code)
        severity = cls.get_error_severity(error.error_code)
        
        # Tạo prefix dựa trên mức độ nghiêm trọng
        severity_prefix = {
            "CRITICAL": "🚨 LỖI NGHIÊM TRỌNG",
            "HIGH": "⚠️ LỖI CAO",
            "MEDIUM": "⚡ LỖI TRUNG BÌNH",
            "LOW": "ℹ️ LỖI NHỎ",
            "NETWORK": "🌐 LỖI MẠNG",
            "UNKNOWN": "❓ LỖI KHÔNG XÁC ĐỊNH"
        }
        
        formatted_message = f"{severity_prefix[severity]}: {message}"
        
        if error.message and error.message != message:
            formatted_message += f"\nChi tiết: {error.message}"
        
        if include_solution:
            solution = cls.get_error_solution(error.error_code)
            formatted_message += f"\n💡 Giải pháp: {solution}"
        
        return formatted_message
    
    @classmethod
    def handle_domain_check_error(cls, domain: str, error: NamecheapAPIError) -> str:
        """
        Xử lý lỗi cụ thể cho domain check
        
        Args:
            domain: Tên domain bị lỗi
            error: NamecheapAPIError object
            
        Returns:
            Thông báo lỗi cho domain cụ thể
        """
        base_message = cls.format_error_message(error, include_solution=False)
        return f"Domain '{domain}': {base_message}"
    
    @classmethod
    def is_recoverable_error(cls, error_code: int) -> bool:
        """
        Kiểm tra xem lỗi có thể khôi phục được không
        
        Args:
            error_code: Mã lỗi
            
        Returns:
            True nếu có thể thử lại, False nếu cần can thiệp thủ công
        """
        # Lỗi có thể thử lại
        recoverable_errors = [0, 3031510, 3011511, 2011169]
        return error_code in recoverable_errors
    
    @classmethod
    def suggest_retry_strategy(cls, error_code: int) -> Optional[str]:
        """
        Đề xuất chiến lược thử lại
        
        Args:
            error_code: Mã lỗi
            
        Returns:
            Chiến lược thử lại hoặc None
        """
        retry_strategies = {
            0: "Thử lại sau 5-10 giây",
            3031510: "Thử lại với ít domain hơn",
            3011511: "Thử lại sau vài phút",
            2011169: "Chia nhỏ danh sách domain (tối đa 50 domain/lần)"
        }
        return retry_strategies.get(error_code)


def handle_api_error(func):
    """
    Decorator để xử lý lỗi API một cách tự động
    
    Args:
        func: Function cần wrap
        
    Returns:
        Wrapped function với error handling
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except NamecheapAPIError as e:
            error_message = ErrorHandler.format_error_message(e)
            print(f"\n{error_message}")
            
            # Đề xuất retry nếu có thể
            if ErrorHandler.is_recoverable_error(e.error_code):
                retry_strategy = ErrorHandler.suggest_retry_strategy(e.error_code)
                if retry_strategy:
                    print(f"🔄 Đề xuất: {retry_strategy}")
            
            return None
        except Exception as e:
            print(f"🚨 LỖI KHÔNG MONG MUỐN: {str(e)}")
            return None
    
    return wrapper
