"""
Test script cho Domain Checker
Script để test các chức năng của ứng dụng
"""

import unittest
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
from namecheap_api import NamecheapAPI, DomainCheckResult, NamecheapAPIError, PricingInfo
from error_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from cli_interface import DomainCheckerCLI


class TestNamecheapAPI(unittest.TestCase):
    """Test cases cho NamecheapAPI"""
    
    def setUp(self):
        """Setup test"""
        # Tạo file config tạm thời
        self.temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.ini', delete=False)
        self.temp_config.write("""
[API]
api_user = test_user
api_key = test_key
username = test_user
client_ip = 127.0.0.1
api_url = https://api.namecheap.com/xml.response

[SETTINGS]
max_domains_per_request = 50
request_timeout = 30
cache_pricing = true
cache_duration = 3600
""")
        self.temp_config.close()
        
        self.api = NamecheapAPI(self.temp_config.name)
    
    def tearDown(self):
        """Cleanup test"""
        os.unlink(self.temp_config.name)
    
    def test_init(self):
        """Test khởi tạo API"""
        self.assertEqual(self.api.api_user, 'test_user')
        self.assertEqual(self.api.api_key, 'test_key')
        self.assertEqual(self.api.username, 'test_user')
        self.assertEqual(self.api.client_ip, '127.0.0.1')
    
    @patch('namecheap_api.requests.get')
    def test_check_domains_success(self, mock_get):
        """Test kiểm tra domain thành công"""
        # Mock response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = '''<?xml version="1.0" encoding="utf-8"?>
<ApiResponse xmlns="http://api.namecheap.com/xml.response" Status="OK">
    <CommandResponse Type="namecheap.domains.check">
        <DomainCheckResult Domain="test.com" Available="true" ErrorNo="0" 
                          IsPremiumName="false" PremiumRegistrationPrice="0" 
                          PremiumRenewalPrice="0" Currency="USD"/>
    </CommandResponse>
</ApiResponse>'''
        mock_get.return_value = mock_response
        
        results = self.api.check_domains(['test.com'])
        
        self.assertEqual(len(results), 1)
        self.assertEqual(results[0].domain, 'test.com')
        self.assertTrue(results[0].available)
        self.assertFalse(results[0].is_premium)
    
    @patch('namecheap_api.requests.get')
    def test_check_domains_premium(self, mock_get):
        """Test kiểm tra premium domain"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = '''<?xml version="1.0" encoding="utf-8"?>
<ApiResponse xmlns="http://api.namecheap.com/xml.response" Status="OK">
    <CommandResponse Type="namecheap.domains.check">
        <DomainCheckResult Domain="premium.com" Available="true" ErrorNo="0" 
                          IsPremiumName="true" PremiumRegistrationPrice="1000.00" 
                          PremiumRenewalPrice="1000.00" Currency="USD"/>
    </CommandResponse>
</ApiResponse>'''
        mock_get.return_value = mock_response
        
        results = self.api.check_domains(['premium.com'])
        
        self.assertEqual(len(results), 1)
        self.assertTrue(results[0].is_premium)
        self.assertEqual(results[0].registration_price, 1000.00)
    
    @patch('namecheap_api.requests.get')
    def test_api_error(self, mock_get):
        """Test xử lý lỗi API"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = '''<?xml version="1.0" encoding="utf-8"?>
<ApiResponse xmlns="http://api.namecheap.com/xml.response" Status="ERROR">
    <Errors>
        <Error Number="1010101">Parameter APIUser is missing</Error>
    </Errors>
</ApiResponse>'''
        mock_get.return_value = mock_response
        
        with self.assertRaises(NamecheapAPIError) as context:
            self.api.check_domains(['test.com'])
        
        self.assertEqual(context.exception.error_code, 1010101)


class TestErrorHandler(unittest.TestCase):
    """Test cases cho ErrorHandler"""
    
    def test_get_error_message(self):
        """Test lấy thông báo lỗi"""
        message = ErrorHandler.get_error_message(1010101)
        self.assertEqual(message, "Thiếu tham số APIUser")
        
        # Test unknown error
        message = ErrorHandler.get_error_message(9999999)
        self.assertIn("Lỗi không xác định", message)
    
    def test_get_error_solution(self):
        """Test lấy giải pháp lỗi"""
        solution = ErrorHandler.get_error_solution(1010101)
        self.assertIn("APIUser", solution)
        self.assertIn("config.ini", solution)
    
    def test_get_error_severity(self):
        """Test lấy mức độ nghiêm trọng"""
        severity = ErrorHandler.get_error_severity(1017101)
        self.assertEqual(severity, "CRITICAL")
        
        severity = ErrorHandler.get_error_severity(1010101)
        self.assertEqual(severity, "HIGH")
        
        severity = ErrorHandler.get_error_severity(0)
        self.assertEqual(severity, "NETWORK")
    
    def test_format_error_message(self):
        """Test format thông báo lỗi"""
        error = NamecheapAPIError(1010101, "Test error")
        formatted = ErrorHandler.format_error_message(error)
        
        self.assertIn("LỖI CAO", formatted)
        self.assertIn("Thiếu tham số APIUser", formatted)
        self.assertIn("Giải pháp", formatted)
    
    def test_is_recoverable_error(self):
        """Test kiểm tra lỗi có thể khôi phục"""
        self.assertTrue(ErrorHandler.is_recoverable_error(0))
        self.assertTrue(ErrorHandler.is_recoverable_error(2011169))
        self.assertFalse(ErrorHandler.is_recoverable_error(1017101))


class TestDomainCheckerCLI(unittest.TestCase):
    """Test cases cho CLI"""
    
    def setUp(self):
        """Setup test"""
        # Mock API
        self.mock_api = Mock()
        
        # Tạo CLI instance với mock API
        with patch('cli_interface.NamecheapAPI') as mock_api_class:
            mock_api_class.return_value = self.mock_api
            self.cli = DomainCheckerCLI()
    
    def test_get_domains_from_file(self):
        """Test đọc domain từ file"""
        # Tạo file tạm thời
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("# Comment line\n")
            f.write("example.com\n")
            f.write("test.net\n")
            f.write("\n")  # Empty line
            f.write("# Another comment\n")
            f.write("sample.org\n")
            temp_filename = f.name
        
        try:
            domains = self.cli.get_domains_from_file(temp_filename)
            expected = ['example.com', 'test.net', 'sample.org']
            self.assertEqual(domains, expected)
        finally:
            os.unlink(temp_filename)
    
    def test_format_result(self):
        """Test format kết quả"""
        # Test available domain
        result = DomainCheckResult(
            domain="test.com",
            available=True,
            is_premium=False,
            error_no=0
        )
        formatted = self.cli.format_result(result)
        self.assertIn("CÓ THỂ MUA", formatted)
        self.assertIn("test.com", formatted)
        
        # Test unavailable domain
        result.available = False
        formatted = self.cli.format_result(result)
        self.assertIn("KHÔNG KHẢ DỤNG", formatted)
        
        # Test premium domain
        result.available = True
        result.is_premium = True
        result.registration_price = 100.0
        result.renewal_price = 100.0
        formatted = self.cli.format_result(result)
        self.assertIn("PREMIUM", formatted)
        self.assertIn("$100.00", formatted)
        
        # Test error
        result.error_no = 1010101
        formatted = self.cli.format_result(result)
        self.assertIn("Lỗi", formatted)


def run_integration_test():
    """Chạy integration test với API thật (nếu có cấu hình)"""
    print("🧪 INTEGRATION TEST")
    print("=" * 50)
    
    if not os.path.exists('config.ini'):
        print("❌ Không tìm thấy config.ini - bỏ qua integration test")
        return
    
    try:
        # Test API connection
        print("1. Test kết nối API...")
        api = NamecheapAPI()
        
        # Test với domain không tồn tại
        test_domains = ['test-domain-integration-12345.com']
        results = api.check_domains(test_domains)
        
        if results:
            print(f"✅ API hoạt động - Kết quả: {len(results)} domain")
            for result in results:
                print(f"   {result.domain}: {'Available' if result.available else 'Not available'}")
        else:
            print("❌ Không nhận được kết quả từ API")
        
        # Test pricing (nếu có)
        print("\n2. Test pricing API...")
        pricing = api.get_pricing('com', 'REGISTER')
        if pricing:
            print(f"✅ Pricing API hoạt động - {len(pricing)} price points")
            for price in pricing[:3]:  # Chỉ hiển thị 3 đầu tiên
                print(f"   {price.duration} {price.duration_type}: ${price.price}")
        else:
            print("⚠️ Không lấy được thông tin pricing")
            
    except NamecheapAPIError as e:
        print(f"❌ API Error: {ErrorHandler.format_error_message(e)}")
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")


def main():
    """Chạy tất cả tests"""
    print("🧪 DOMAIN CHECKER - TEST SUITE")
    print("=" * 50)
    
    # Unit tests
    print("\n📋 UNIT TESTS:")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Integration test
    print("\n🔗 INTEGRATION TEST:")
    run_integration_test()
    
    print("\n✅ Test hoàn thành!")


if __name__ == "__main__":
    main()
