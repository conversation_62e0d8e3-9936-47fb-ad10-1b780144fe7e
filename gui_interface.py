"""
GUI Interface cho Domain Checker
Gia<PERSON> diện đồ họa sử dụng tkinter
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import threading
from typing import List
from namecheap_api import NamecheapAPI, DomainCheckResult, NamecheapAPIError
from error_handler import ErrorHandler


class DomainCheckerGUI:
    """GUI cho Domain Checker"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Namecheap Domain Checker")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # API client
        self.api = None
        self.is_checking = False
        
        self.setup_ui()
        self.load_api_config()
    
    def setup_ui(self):
        """Thiết lập giao diện người dùng"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Namecheap Domain Availability Checker", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Input section
        input_frame = ttk.LabelFrame(main_frame, text="Nhập danh sách domain", padding="10")
        input_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        input_frame.columnconfigure(0, weight=1)
        
        # Domain input
        ttk.Label(input_frame, text="Nhập domain names (mỗi domain một dòng):").grid(row=0, column=0, sticky=tk.W)
        
        self.domain_text = scrolledtext.ScrolledText(input_frame, height=8, width=50)
        self.domain_text.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(5, 10))
        
        # Buttons frame
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        self.check_button = ttk.Button(button_frame, text="Kiểm tra Domain", 
                                      command=self.start_domain_check)
        self.check_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="Xóa", command=self.clear_input).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Tải từ file", command=self.load_from_file).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="Lưu kết quả", command=self.save_results).pack(side=tk.LEFT)
        
        # Progress bar
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # Results section
        results_frame = ttk.LabelFrame(main_frame, text="Kết quả kiểm tra", padding="10")
        results_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # Results treeview
        columns = ("Domain", "Trạng thái", "Loại", "Giá đăng ký", "Giá gia hạn", "Tiền tệ")
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=15)
        
        # Configure columns
        self.results_tree.heading("Domain", text="Domain")
        self.results_tree.heading("Trạng thái", text="Trạng thái")
        self.results_tree.heading("Loại", text="Loại")
        self.results_tree.heading("Giá đăng ký", text="Giá đăng ký")
        self.results_tree.heading("Giá gia hạn", text="Giá gia hạn")
        self.results_tree.heading("Tiền tệ", text="Tiền tệ")
        
        self.results_tree.column("Domain", width=200)
        self.results_tree.column("Trạng thái", width=100)
        self.results_tree.column("Loại", width=80)
        self.results_tree.column("Giá đăng ký", width=100)
        self.results_tree.column("Giá gia hạn", width=100)
        self.results_tree.column("Tiền tệ", width=60)
        
        # Scrollbar for treeview
        scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=scrollbar.set)
        
        self.results_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Status bar
        self.status_var = tk.StringVar()
        self.status_var.set("Sẵn sàng")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E))
        
        # Store results for saving
        self.last_results = []
    
    def load_api_config(self):
        """Tải cấu hình API"""
        try:
            self.api = NamecheapAPI()
            self.status_var.set("API đã sẵn sàng")
        except Exception as e:
            self.status_var.set("Lỗi cấu hình API")
            messagebox.showerror("Lỗi cấu hình", 
                               f"Không thể tải cấu hình API:\n{str(e)}\n\n"
                               "Vui lòng kiểm tra file config.ini")
    
    def clear_input(self):
        """Xóa input"""
        self.domain_text.delete(1.0, tk.END)
    
    def load_from_file(self):
        """Tải danh sách domain từ file"""
        filename = filedialog.askopenfilename(
            title="Chọn file chứa danh sách domain",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.domain_text.delete(1.0, tk.END)
                    self.domain_text.insert(1.0, content)
                self.status_var.set(f"Đã tải file: {filename}")
            except Exception as e:
                messagebox.showerror("Lỗi", f"Không thể đọc file:\n{str(e)}")
    
    def save_results(self):
        """Lưu kết quả ra file"""
        if not self.last_results:
            messagebox.showwarning("Cảnh báo", "Không có kết quả để lưu")
            return
        
        filename = filedialog.asksaveasfilename(
            title="Lưu kết quả",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("Domain\tTrạng thái\tLoại\tGiá đăng ký\tGiá gia hạn\tTiền tệ\n")
                    for result in self.last_results:
                        status = "Có thể mua" if result.available else "Không khả dụng"
                        domain_type = "Premium" if result.is_premium else "Thường"
                        reg_price = f"{result.registration_price:.2f}" if result.registration_price > 0 else "N/A"
                        renew_price = f"{result.renewal_price:.2f}" if result.renewal_price > 0 else "N/A"
                        
                        f.write(f"{result.domain}\t{status}\t{domain_type}\t{reg_price}\t{renew_price}\t{result.currency}\n")
                
                self.status_var.set(f"Đã lưu kết quả: {filename}")
                messagebox.showinfo("Thành công", f"Đã lưu kết quả vào:\n{filename}")
            except Exception as e:
                messagebox.showerror("Lỗi", f"Không thể lưu file:\n{str(e)}")
    
    def get_domains_from_input(self) -> List[str]:
        """Lấy danh sách domain từ input"""
        content = self.domain_text.get(1.0, tk.END).strip()
        if not content:
            return []
        
        domains = []
        for line in content.split('\n'):
            domain = line.strip()
            if domain and not domain.startswith('#'):  # Bỏ qua comment
                domains.append(domain)
        
        return domains
    
    def start_domain_check(self):
        """Bắt đầu kiểm tra domain trong thread riêng"""
        if self.is_checking:
            return
        
        if not self.api:
            messagebox.showerror("Lỗi", "API chưa được cấu hình")
            return
        
        domains = self.get_domains_from_input()
        if not domains:
            messagebox.showwarning("Cảnh báo", "Vui lòng nhập ít nhất một domain")
            return
        
        # Disable button và start progress
        self.check_button.config(state='disabled')
        self.progress.start()
        self.is_checking = True
        self.status_var.set(f"Đang kiểm tra {len(domains)} domain...")
        
        # Clear previous results
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)
        
        # Start checking in separate thread
        thread = threading.Thread(target=self.check_domains_thread, args=(domains,))
        thread.daemon = True
        thread.start()
    
    def check_domains_thread(self, domains: List[str]):
        """Kiểm tra domain trong thread riêng"""
        try:
            results = self.api.check_domains(domains)
            self.last_results = results
            
            # Update UI in main thread
            self.root.after(0, self.update_results, results)
            
        except Exception as e:
            error_msg = str(e)
            if isinstance(e, NamecheapAPIError):
                error_msg = ErrorHandler.format_error_message(e)
            
            self.root.after(0, self.show_error, error_msg)
        finally:
            self.root.after(0, self.finish_checking)
    
    def update_results(self, results: List[DomainCheckResult]):
        """Cập nhật kết quả lên UI"""
        for result in results:
            if result.error_no != 0:
                # Hiển thị lỗi
                status = f"Lỗi ({result.error_no})"
                domain_type = "N/A"
                reg_price = "N/A"
                renew_price = "N/A"
                currency = "N/A"
                
                # Insert with error styling
                item = self.results_tree.insert("", tk.END, values=(
                    result.domain, status, domain_type, reg_price, renew_price, currency
                ))
                self.results_tree.set(item, "Domain", result.domain)
                
            else:
                status = "Có thể mua" if result.available else "Không khả dụng"
                domain_type = "Premium" if result.is_premium else "Thường"
                
                if result.available and result.is_premium:
                    reg_price = f"{result.registration_price:.2f}" if result.registration_price > 0 else "N/A"
                    renew_price = f"{result.renewal_price:.2f}" if result.renewal_price > 0 else "N/A"
                    currency = result.currency
                else:
                    reg_price = "Cần kiểm tra giá"
                    renew_price = "Cần kiểm tra giá"
                    currency = "USD"
                
                # Insert result
                item = self.results_tree.insert("", tk.END, values=(
                    result.domain, status, domain_type, reg_price, renew_price, currency
                ))
                
                # Color coding
                if result.available:
                    self.results_tree.set(item, "Trạng thái", "✅ " + status)
                else:
                    self.results_tree.set(item, "Trạng thái", "❌ " + status)
        
        self.status_var.set(f"Hoàn thành kiểm tra {len(results)} domain")
    
    def show_error(self, error_msg: str):
        """Hiển thị lỗi"""
        messagebox.showerror("Lỗi", error_msg)
        self.status_var.set("Lỗi khi kiểm tra domain")
    
    def finish_checking(self):
        """Kết thúc quá trình kiểm tra"""
        self.progress.stop()
        self.check_button.config(state='normal')
        self.is_checking = False
    
    def run(self):
        """Chạy ứng dụng"""
        self.root.mainloop()


if __name__ == "__main__":
    app = DomainCheckerGUI()
    app.run()
