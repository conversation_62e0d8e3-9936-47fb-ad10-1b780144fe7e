@echo off
echo Installing Namecheap Domain Checker...
echo.

echo 1. Installing Python dependencies...
pip install -r requirements.txt

echo.
echo 2. Creating config file...
if not exist config.ini (
    copy config_example.ini config.ini
    echo Config file created from example.
    echo Please edit config.ini with your API credentials.
) else (
    echo Config file already exists.
)

echo.
echo 3. Creating sample domain file...
python main.py --sample

echo.
echo 4. Testing API connection...
python main.py --test

echo.
echo Installation complete!
echo.
echo Next steps:
echo 1. Edit config.ini with your Namecheap API credentials
echo 2. Run: python main.py --gui (for GUI)
echo 3. Run: python main.py (for CLI)
echo.
pause
