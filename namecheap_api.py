"""
Namecheap API Client
Module để gọi Namecheap API cho việc kiểm tra domain availability và pricing
"""

import requests
import xml.etree.ElementTree as ET
from typing import List, Dict, Optional, Tuple
import configparser
import time
from dataclasses import dataclass


@dataclass
class DomainCheckResult:
    """Kết quả kiểm tra domain"""
    domain: str
    available: bool
    is_premium: bool = False
    registration_price: float = 0.0
    renewal_price: float = 0.0
    restore_price: float = 0.0
    transfer_price: float = 0.0
    icann_fee: float = 0.0
    eap_fee: float = 0.0
    currency: str = "USD"
    error_no: int = 0
    description: str = ""


@dataclass
class PricingInfo:
    """Thông tin giá domain"""
    tld: str
    duration: int
    duration_type: str
    price: float
    regular_price: float
    your_price: float
    coupon_price: float
    currency: str


class NamecheapAPIError(Exception):
    """Custom exception cho Namecheap API errors"""
    def __init__(self, error_code: int, message: str):
        self.error_code = error_code
        self.message = message
        super().__init__(f"Error {error_code}: {message}")


class NamecheapAPI:
    """Client để gọi Namecheap API"""
    
    def __init__(self, config_file: str = "config.ini"):
        """
        Khởi tạo API client
        
        Args:
            config_file: Đường dẫn đến file cấu hình
        """
        self.config = configparser.ConfigParser()
        self.config.read(config_file)
        
        # API credentials
        self.api_user = self.config.get('API', 'api_user')
        self.api_key = self.config.get('API', 'api_key')
        self.username = self.config.get('API', 'username')
        self.client_ip = self.config.get('API', 'client_ip')
        self.api_url = self.config.get('API', 'api_url')
        
        # Settings
        self.max_domains = int(self.config.get('SETTINGS', 'max_domains_per_request'))
        self.timeout = int(self.config.get('SETTINGS', 'request_timeout'))
        
        # Cache cho pricing
        self._pricing_cache = {}
        self._cache_duration = int(self.config.get('SETTINGS', 'cache_duration'))
        
    def _make_request(self, command: str, params: Dict[str, str]) -> ET.Element:
        """
        Thực hiện request đến Namecheap API
        
        Args:
            command: API command
            params: Tham số bổ sung
            
        Returns:
            XML response root element
            
        Raises:
            NamecheapAPIError: Khi có lỗi từ API
        """
        # Global parameters
        request_params = {
            'ApiUser': self.api_user,
            'ApiKey': self.api_key,
            'UserName': self.username,
            'Command': command,
            'ClientIp': self.client_ip
        }
        
        # Thêm params bổ sung
        request_params.update(params)
        
        try:
            response = requests.get(self.api_url, params=request_params, timeout=self.timeout)
            response.raise_for_status()
            
            # Parse XML response
            root = ET.fromstring(response.text)
            
            # Kiểm tra status
            status = root.get('Status')
            if status != 'OK':
                # Lấy error message
                errors = root.find('.//{http://api.namecheap.com/xml.response}Errors')
                if errors is not None:
                    error = errors.find('.//{http://api.namecheap.com/xml.response}Error')
                    if error is not None:
                        error_code = int(error.get('Number', 0))
                        error_msg = error.text or "Unknown error"
                        raise NamecheapAPIError(error_code, error_msg)
                
                raise NamecheapAPIError(0, "Unknown API error")
            
            return root
            
        except requests.RequestException as e:
            raise NamecheapAPIError(0, f"Network error: {str(e)}")
        except ET.ParseError as e:
            raise NamecheapAPIError(0, f"XML parsing error: {str(e)}")
    
    def check_domains(self, domains: List[str]) -> List[DomainCheckResult]:
        """
        Kiểm tra availability của danh sách domains
        
        Args:
            domains: Danh sách domain names
            
        Returns:
            Danh sách kết quả kiểm tra
        """
        results = []
        
        # Chia domains thành chunks để không vượt quá giới hạn
        for i in range(0, len(domains), self.max_domains):
            chunk = domains[i:i + self.max_domains]
            domain_list = ','.join(chunk)
            
            try:
                root = self._make_request('namecheap.domains.check', {
                    'DomainList': domain_list
                })
                
                # Parse kết quả
                command_response = root.find('.//{http://api.namecheap.com/xml.response}CommandResponse')
                if command_response is not None:
                    for domain_result in command_response.findall('.//{http://api.namecheap.com/xml.response}DomainCheckResult'):
                        result = DomainCheckResult(
                            domain=domain_result.get('Domain', ''),
                            available=domain_result.get('Available', 'false').lower() == 'true',
                            is_premium=domain_result.get('IsPremiumName', 'false').lower() == 'true',
                            registration_price=float(domain_result.get('PremiumRegistrationPrice', 0)),
                            renewal_price=float(domain_result.get('PremiumRenewalPrice', 0)),
                            restore_price=float(domain_result.get('PremiumRestorePrice', 0)),
                            transfer_price=float(domain_result.get('PremiumTransferPrice', 0)),
                            icann_fee=float(domain_result.get('IcannFee', 0)),
                            eap_fee=float(domain_result.get('EapFee', 0)),
                            error_no=int(domain_result.get('ErrorNo', 0)),
                            description=domain_result.get('Description', '')
                        )
                        results.append(result)
                        
            except NamecheapAPIError as e:
                # Thêm error result cho tất cả domains trong chunk
                for domain in chunk:
                    results.append(DomainCheckResult(
                        domain=domain,
                        available=False,
                        error_no=e.error_code,
                        description=e.message
                    ))
        
        return results
    
    def get_pricing(self, tld: str, action: str = "REGISTER") -> List[PricingInfo]:
        """
        Lấy thông tin giá cho TLD
        
        Args:
            tld: Top Level Domain (ví dụ: "com", "net")
            action: Loại action ("REGISTER", "RENEW", "TRANSFER")
            
        Returns:
            Danh sách thông tin giá
        """
        cache_key = f"{tld}_{action}"
        
        # Kiểm tra cache
        if cache_key in self._pricing_cache:
            cached_time, cached_data = self._pricing_cache[cache_key]
            if time.time() - cached_time < self._cache_duration:
                return cached_data
        
        try:
            root = self._make_request('namecheap.users.getPricing', {
                'ProductType': 'DOMAIN',
                'ProductCategory': action,
                'ProductName': tld.upper()
            })
            
            pricing_info = []
            command_response = root.find('.//{http://api.namecheap.com/xml.response}CommandResponse')
            
            if command_response is not None:
                for product in command_response.findall('.//{http://api.namecheap.com/xml.response}Product'):
                    if product.get('Name', '').lower() == tld.lower():
                        for price in product.findall('.//{http://api.namecheap.com/xml.response}Price'):
                            info = PricingInfo(
                                tld=tld,
                                duration=int(price.get('Duration', 1)),
                                duration_type=price.get('DurationType', 'YEAR'),
                                price=float(price.get('Price', 0)),
                                regular_price=float(price.get('RegularPrice', 0)),
                                your_price=float(price.get('YourPrice', 0)),
                                coupon_price=float(price.get('CouponPrice', 0) or 0),
                                currency=price.get('Currency', 'USD')
                            )
                            pricing_info.append(info)
            
            # Cache kết quả
            self._pricing_cache[cache_key] = (time.time(), pricing_info)
            return pricing_info
            
        except NamecheapAPIError:
            return []
