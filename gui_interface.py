"""
GUI Interface cho Domain Checker
G<PERSON><PERSON> diện đ<PERSON> họa sử dụng tkinter với modern styling
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, filedialog
import threading
from typing import List, Dict
import re
from namecheap_api import NamecheapAPI, DomainCheckResult, NamecheapAPIError
from error_handler import ErrorHandler


class ToolTip:
    """Tooltip class cho widgets"""
    def __init__(self, widget, text):
        self.widget = widget
        self.text = text
        self.tooltip_window = None
        self.widget.bind("<Enter>", self.on_enter)
        self.widget.bind("<Leave>", self.on_leave)

    def on_enter(self, event=None):
        """Hiển thị tooltip khi hover"""
        if self.tooltip_window or not self.text:
            return

        x, y, _, _ = self.widget.bbox("insert") if hasattr(self.widget, 'bbox') else (0, 0, 0, 0)
        x += self.widget.winfo_rootx() + 25
        y += self.widget.winfo_rooty() + 25

        self.tooltip_window = tw = tk.Toplevel(self.widget)
        tw.wm_overrideredirect(True)
        tw.wm_geometry(f"+{x}+{y}")

        label = tk.Label(tw, text=self.text, justify=tk.LEFT,
                        background="#ffffe0", relief=tk.SOLID, borderwidth=1,
                        font=("Segoe UI", 8))
        label.pack(ipadx=1)

    def on_leave(self, event=None):
        """Ẩn tooltip khi không hover"""
        if self.tooltip_window:
            self.tooltip_window.destroy()
            self.tooltip_window = None


class DomainCheckerGUI:
    """GUI cho Domain Checker với modern styling"""

    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🌐 Namecheap Domain Checker")
        self.root.geometry("1000x700")
        self.root.resizable(True, True)

        # Set window icon and styling
        self.root.configure(bg='#f0f0f0')

        # API client
        self.api = None
        self.is_checking = False

        # Pricing cache để tránh gọi API nhiều lần
        self.pricing_cache = {}

        # Setup modern theme
        self.setup_theme()
        self.setup_ui()
        self.load_api_config()
    
    def setup_theme(self):
        """Thiết lập theme hiện đại cho ứng dụng"""
        style = ttk.Style()

        # Configure modern theme
        style.theme_use('clam')

        # Custom colors
        colors = {
            'primary': '#2196F3',      # Blue
            'success': '#4CAF50',      # Green
            'warning': '#FF9800',      # Orange
            'danger': '#F44336',       # Red
            'dark': '#212121',         # Dark gray
            'light': '#FAFAFA',        # Light gray
            'white': '#FFFFFF'
        }

        # Configure styles
        style.configure('Title.TLabel',
                       font=('Segoe UI', 18, 'bold'),
                       foreground=colors['dark'],
                       background='#f0f0f0')

        style.configure('Heading.TLabel',
                       font=('Segoe UI', 10, 'bold'),
                       foreground=colors['dark'])

        style.configure('Primary.TButton',
                       font=('Segoe UI', 9, 'bold'),
                       foreground='white',
                       background=colors['primary'])

        style.map('Primary.TButton',
                 background=[('active', '#1976D2')])

        style.configure('Success.TButton',
                       font=('Segoe UI', 9),
                       foreground='white',
                       background=colors['success'])

        style.configure('Warning.TButton',
                       font=('Segoe UI', 9),
                       foreground='white',
                       background=colors['warning'])

        style.configure('Custom.Treeview',
                       font=('Segoe UI', 9),
                       rowheight=25)

        style.configure('Custom.Treeview.Heading',
                       font=('Segoe UI', 9, 'bold'),
                       foreground=colors['dark'])

    def setup_ui(self):
        """Thiết lập giao diện người dùng với modern design"""
        # Main container
        main_container = ttk.Frame(self.root)
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

        # Configure grid weights
        main_container.columnconfigure(0, weight=1)
        main_container.rowconfigure(2, weight=1)
        
        # Header section
        header_frame = ttk.Frame(main_container)
        header_frame.pack(fill=tk.X, pady=(0, 20))

        # Title with icon
        title_label = ttk.Label(header_frame,
                               text="🌐 Namecheap Domain Availability Checker",
                               style='Title.TLabel')
        title_label.pack()

        # Subtitle
        subtitle_label = ttk.Label(header_frame,
                                  text="Kiểm tra tính khả dụng và giá của domain names",
                                  font=('Segoe UI', 10),
                                  foreground='#666666')
        subtitle_label.pack(pady=(5, 0))

        # Input section với modern styling
        input_frame = ttk.LabelFrame(main_container, text="📝 Nhập danh sách domain", padding="15")
        input_frame.pack(fill=tk.X, pady=(0, 15))
        input_frame.columnconfigure(0, weight=1)
        
        # Domain input với instruction
        instruction_label = ttk.Label(input_frame,
                                     text="💡 Nhập domain names (mỗi domain một dòng):",
                                     style='Heading.TLabel')
        instruction_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 8))

        # Text area với modern styling
        text_frame = ttk.Frame(input_frame)
        text_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        text_frame.columnconfigure(0, weight=1)

        self.domain_text = scrolledtext.ScrolledText(text_frame,
                                                    height=6,
                                                    font=('Consolas', 10),
                                                    wrap=tk.WORD,
                                                    relief=tk.FLAT,
                                                    borderwidth=1)
        self.domain_text.pack(fill=tk.BOTH, expand=True)

        # Placeholder text
        placeholder_text = "# Ví dụ:\nexample.com\nmydomain.net\nawesome-site.org"
        self.domain_text.insert(1.0, placeholder_text)
        self.domain_text.bind('<FocusIn>', self.clear_placeholder)
        self.domain_text.bind('<FocusOut>', self.add_placeholder)
        self.placeholder_active = True

        # Buttons frame với modern styling
        button_frame = ttk.Frame(input_frame)
        button_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E))

        # Primary action button
        self.check_button = ttk.Button(button_frame,
                                      text="🔍 Kiểm tra Domain",
                                      style='Primary.TButton',
                                      command=self.start_domain_check)
        self.check_button.pack(side=tk.LEFT, padx=(0, 10))
        self.create_tooltip(self.check_button, "Kiểm tra tính khả dụng và giá của domains (Ctrl+Enter)")

        # Secondary buttons
        clear_btn = ttk.Button(button_frame, text="🗑️ Xóa", command=self.clear_input)
        clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        self.create_tooltip(clear_btn, "Xóa tất cả domain trong danh sách (Delete)")

        load_btn = ttk.Button(button_frame, text="📁 Tải từ file", command=self.load_from_file)
        load_btn.pack(side=tk.LEFT, padx=(0, 10))
        self.create_tooltip(load_btn, "Tải danh sách domain từ file text (Ctrl+O)")

        self.save_button = ttk.Button(button_frame, text="💾 Lưu kết quả",
                                     style='Success.TButton',
                                     command=self.save_results)
        self.save_button.pack(side=tk.LEFT)
        self.save_button.config(state='disabled')
        self.create_tooltip(self.save_button, "Lưu kết quả kiểm tra ra file (Ctrl+S)")
        
        # Progress section
        progress_frame = ttk.Frame(main_container)
        progress_frame.pack(fill=tk.X, pady=(0, 15))

        # Progress bar với label
        self.progress_label = ttk.Label(progress_frame, text="Sẵn sàng kiểm tra", font=('Segoe UI', 9))
        self.progress_label.pack(anchor=tk.W, pady=(0, 5))

        self.progress = ttk.Progressbar(progress_frame, mode='indeterminate', height=8)
        self.progress.pack(fill=tk.X)

        # Results section với modern styling
        results_frame = ttk.LabelFrame(main_container, text="📊 Kết quả kiểm tra", padding="15")
        results_frame.pack(fill=tk.BOTH, expand=True)
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # Results treeview với modern styling
        columns = ("Domain", "Trạng thái", "Loại", "Giá đăng ký", "Giá gia hạn", "Tiền tệ")
        self.results_tree = ttk.Treeview(results_frame,
                                        columns=columns,
                                        show="headings",
                                        height=12,
                                        style='Custom.Treeview')

        # Configure columns với icons
        self.results_tree.heading("Domain", text="🌐 Domain")
        self.results_tree.heading("Trạng thái", text="📊 Trạng thái")
        self.results_tree.heading("Loại", text="🏷️ Loại")
        self.results_tree.heading("Giá đăng ký", text="💰 Giá đăng ký")
        self.results_tree.heading("Giá gia hạn", text="🔄 Giá gia hạn")
        self.results_tree.heading("Tiền tệ", text="💱 Tiền tệ")

        # Improved column widths
        self.results_tree.column("Domain", width=250, minwidth=200)
        self.results_tree.column("Trạng thái", width=120, minwidth=100)
        self.results_tree.column("Loại", width=80, minwidth=70)
        self.results_tree.column("Giá đăng ký", width=120, minwidth=100)
        self.results_tree.column("Giá gia hạn", width=120, minwidth=100)
        self.results_tree.column("Tiền tệ", width=80, minwidth=60)

        # Configure tags for styling
        self.results_tree.tag_configure('available', background='#E8F5E8', foreground='#2E7D32')
        self.results_tree.tag_configure('unavailable', background='#FFEBEE', foreground='#C62828')
        self.results_tree.tag_configure('premium', background='#FFF3E0', foreground='#F57C00')
        self.results_tree.tag_configure('error', background='#FFEBEE', foreground='#D32F2F')
        
        # Scrollbars for treeview
        tree_frame = ttk.Frame(results_frame)
        tree_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)

        # Vertical scrollbar
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=v_scrollbar.set)

        # Horizontal scrollbar
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.results_tree.xview)
        self.results_tree.configure(xscrollcommand=h_scrollbar.set)

        # Grid treeview and scrollbars
        self.results_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))

        # Status bar với modern styling
        status_frame = ttk.Frame(main_container)
        status_frame.pack(fill=tk.X, pady=(10, 0))

        self.status_var = tk.StringVar()
        self.status_var.set("🟢 Sẵn sàng")
        status_bar = ttk.Label(status_frame,
                              textvariable=self.status_var,
                              font=('Segoe UI', 9),
                              foreground='#666666')
        status_bar.pack(anchor=tk.W)

        # Store results for saving
        self.last_results = []

        # Add context menu for treeview
        self.setup_context_menu()

        # Add keyboard shortcuts
        self.setup_keyboard_shortcuts()

    def setup_context_menu(self):
        """Thiết lập context menu cho treeview"""
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="📋 Copy Domain", command=self.copy_selected_domain)
        self.context_menu.add_command(label="🌐 Mở trong trình duyệt", command=self.open_domain_in_browser)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🔍 Kiểm tra lại", command=self.recheck_selected_domain)

        self.results_tree.bind("<Button-3>", self.show_context_menu)  # Right click

    def setup_keyboard_shortcuts(self):
        """Thiết lập keyboard shortcuts"""
        self.root.bind('<Control-Return>', lambda e: self.start_domain_check())
        self.root.bind('<Control-s>', lambda e: self.save_results())
        self.root.bind('<Control-o>', lambda e: self.load_from_file())
        self.root.bind('<Delete>', lambda e: self.clear_input())
        self.root.bind('<F5>', lambda e: self.start_domain_check())

    def show_context_menu(self, event):
        """Hiển thị context menu"""
        item = self.results_tree.selection()
        if item:
            self.context_menu.post(event.x_root, event.y_root)

    def copy_selected_domain(self):
        """Copy domain được chọn vào clipboard"""
        selection = self.results_tree.selection()
        if selection:
            item = selection[0]
            domain = self.results_tree.item(item)['values'][0]
            self.root.clipboard_clear()
            self.root.clipboard_append(domain)
            self.status_var.set(f"📋 Đã copy: {domain}")

    def open_domain_in_browser(self):
        """Mở domain trong trình duyệt"""
        selection = self.results_tree.selection()
        if selection:
            item = selection[0]
            domain = self.results_tree.item(item)['values'][0]
            import webbrowser
            webbrowser.open(f"http://{domain}")

    def recheck_selected_domain(self):
        """Kiểm tra lại domain được chọn"""
        selection = self.results_tree.selection()
        if selection:
            item = selection[0]
            domain = self.results_tree.item(item)['values'][0]
            self.domain_text.delete(1.0, tk.END)
            self.domain_text.insert(1.0, domain)
            self.domain_text.config(foreground='black')
            self.placeholder_active = False
            self.start_domain_check()

    def create_tooltip(self, widget, text):
        """Tạo tooltip cho widget"""
        ToolTip(widget, text)
    
    def clear_placeholder(self, event):
        """Xóa placeholder text khi focus vào text area"""
        if self.placeholder_active:
            self.domain_text.delete(1.0, tk.END)
            self.domain_text.config(foreground='black')
            self.placeholder_active = False

    def add_placeholder(self, event):
        """Thêm placeholder text khi text area trống và mất focus"""
        if not self.domain_text.get(1.0, tk.END).strip():
            self.domain_text.delete(1.0, tk.END)
            self.domain_text.insert(1.0, "# Ví dụ:\nexample.com\nmydomain.net\nawesome-site.org")
            self.domain_text.config(foreground='gray')
            self.placeholder_active = True

    def load_api_config(self):
        """Tải cấu hình API"""
        try:
            self.api = NamecheapAPI()
            self.status_var.set("🟢 API đã sẵn sàng")
        except Exception as e:
            self.status_var.set("🔴 Lỗi cấu hình API")
            messagebox.showerror("Lỗi cấu hình",
                               f"Không thể tải cấu hình API:\n{str(e)}\n\n"
                               "Vui lòng kiểm tra file config.ini")
    
    def clear_input(self):
        """Xóa input và reset placeholder"""
        self.domain_text.delete(1.0, tk.END)
        self.add_placeholder(None)
    
    def load_from_file(self):
        """Tải danh sách domain từ file"""
        filename = filedialog.askopenfilename(
            title="Chọn file chứa danh sách domain",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    content = f.read()
                    self.domain_text.delete(1.0, tk.END)
                    self.domain_text.insert(1.0, content)
                    self.domain_text.config(foreground='black')
                    self.placeholder_active = False
                self.status_var.set(f"📁 Đã tải file: {filename}")
            except Exception as e:
                messagebox.showerror("Lỗi", f"Không thể đọc file:\n{str(e)}")
    
    def save_results(self):
        """Lưu kết quả ra file"""
        if not self.last_results:
            messagebox.showwarning("⚠️ Cảnh báo", "Không có kết quả để lưu")
            return
        
        filename = filedialog.asksaveasfilename(
            title="Lưu kết quả",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("CSV files", "*.csv"), ("All files", "*.*")]
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write("Domain\tTrạng thái\tLoại\tGiá đăng ký\tGiá gia hạn\tTiền tệ\n")
                    for result in self.last_results:
                        status = "Có thể mua" if result.available else "Không khả dụng"
                        domain_type = "Premium" if result.is_premium else "Thường"
                        reg_price = f"{result.registration_price:.2f}" if result.registration_price > 0 else "N/A"
                        renew_price = f"{result.renewal_price:.2f}" if result.renewal_price > 0 else "N/A"
                        
                        f.write(f"{result.domain}\t{status}\t{domain_type}\t{reg_price}\t{renew_price}\t{result.currency}\n")
                
                self.status_var.set(f"💾 Đã lưu kết quả: {filename}")
                messagebox.showinfo("✅ Thành công", f"Đã lưu kết quả vào:\n{filename}")
            except Exception as e:
                messagebox.showerror("Lỗi", f"Không thể lưu file:\n{str(e)}")
    
    def get_domains_from_input(self) -> List[str]:
        """Lấy danh sách domain từ input"""
        if self.placeholder_active:
            return []

        content = self.domain_text.get(1.0, tk.END).strip()
        if not content:
            return []

        domains = []
        for line in content.split('\n'):
            domain = line.strip()
            if domain and not domain.startswith('#'):  # Bỏ qua comment
                # Validate domain format
                if self.is_valid_domain(domain):
                    domains.append(domain.lower())

        return domains

    def is_valid_domain(self, domain: str) -> bool:
        """Kiểm tra format domain có hợp lệ không"""
        # Basic domain validation
        pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        return re.match(pattern, domain) is not None and len(domain) <= 253
    
    def start_domain_check(self):
        """Bắt đầu kiểm tra domain trong thread riêng"""
        if self.is_checking:
            return
        
        if not self.api:
            messagebox.showerror("❌ Lỗi", "API chưa được cấu hình")
            return

        domains = self.get_domains_from_input()
        if not domains:
            messagebox.showwarning("⚠️ Cảnh báo", "Vui lòng nhập ít nhất một domain hợp lệ")
            return

        # Disable button và start progress
        self.check_button.config(state='disabled')
        self.save_button.config(state='disabled')
        self.progress.start()
        self.is_checking = True
        self.status_var.set(f"🔍 Đang kiểm tra {len(domains)} domain...")
        self.progress_label.config(text=f"Đang kiểm tra {len(domains)} domain...")

        # Clear previous results
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # Start checking in separate thread
        thread = threading.Thread(target=self.check_domains_thread, args=(domains,))
        thread.daemon = True
        thread.start()
    
    def get_tld_from_domain(self, domain: str) -> str:
        """Lấy TLD từ domain name"""
        parts = domain.split('.')
        if len(parts) >= 2:
            return parts[-1]
        return ""

    def get_pricing_for_tld(self, tld: str) -> Dict[str, float]:
        """Lấy pricing cho TLD với cache"""
        if tld in self.pricing_cache:
            return self.pricing_cache[tld]

        try:
            pricing_info = self.api.get_pricing(tld, "REGISTER")
            if pricing_info:
                # Lấy giá cho 1 năm đầu tiên
                for price in pricing_info:
                    if price.duration == 1:
                        pricing_data = {
                            'registration': price.price,
                            'renewal': price.price,  # Thường giống nhau cho domain thường
                            'currency': price.currency
                        }
                        self.pricing_cache[tld] = pricing_data
                        return pricing_data

            # Nếu không lấy được pricing, return default
            default_pricing = {'registration': 0, 'renewal': 0, 'currency': 'USD'}
            self.pricing_cache[tld] = default_pricing
            return default_pricing

        except Exception:
            # Cache lỗi để tránh gọi lại
            default_pricing = {'registration': 0, 'renewal': 0, 'currency': 'USD'}
            self.pricing_cache[tld] = default_pricing
            return default_pricing

    def check_domains_thread(self, domains: List[str]):
        """Kiểm tra domain trong thread riêng với pricing"""
        try:
            # Step 1: Check domain availability
            self.root.after(0, lambda: self.progress_label.config(text="Đang kiểm tra tính khả dụng..."))
            results = self.api.check_domains(domains)

            # Step 2: Get pricing for available domains
            self.root.after(0, lambda: self.progress_label.config(text="Đang lấy thông tin giá..."))

            # Collect unique TLDs
            unique_tlds = set()
            for result in results:
                if result.available and not result.is_premium:
                    tld = self.get_tld_from_domain(result.domain)
                    if tld:
                        unique_tlds.add(tld)

            # Get pricing for each TLD
            for i, tld in enumerate(unique_tlds):
                self.root.after(0, lambda t=tld, idx=i, total=len(unique_tlds):
                               self.progress_label.config(text=f"Đang lấy giá cho .{t} ({idx+1}/{total})..."))
                self.get_pricing_for_tld(tld)

            self.last_results = results

            # Update UI in main thread
            self.root.after(0, self.update_results, results)

        except Exception as e:
            error_msg = str(e)
            if isinstance(e, NamecheapAPIError):
                error_msg = ErrorHandler.format_error_message(e)

            self.root.after(0, self.show_error, error_msg)
        finally:
            self.root.after(0, self.finish_checking)
    
    def update_results(self, results: List[DomainCheckResult]):
        """Cập nhật kết quả lên UI với giá chi tiết"""
        available_count = 0
        unavailable_count = 0
        error_count = 0

        for result in results:
            if result.error_no != 0:
                # Hiển thị lỗi
                status = f"Lỗi ({result.error_no})"
                domain_type = "N/A"
                reg_price = "N/A"
                renew_price = "N/A"
                currency = "N/A"
                tag = 'error'
                error_count += 1

                # Insert with error styling
                item = self.results_tree.insert("", tk.END, values=(
                    result.domain, f"❌ {status}", domain_type, reg_price, renew_price, currency
                ), tags=(tag,))

            else:
                status = "Có thể mua" if result.available else "Không khả dụng"
                domain_type = "Premium" if result.is_premium else "Thường"

                if result.available:
                    available_count += 1

                    if result.is_premium:
                        # Premium domain - sử dụng giá từ API response
                        reg_price = f"${result.registration_price:.2f}" if result.registration_price > 0 else "N/A"
                        renew_price = f"${result.renewal_price:.2f}" if result.renewal_price > 0 else "N/A"
                        currency = result.currency or "USD"
                        tag = 'premium'
                        status_icon = "💎"
                    else:
                        # Domain thường - lấy giá từ pricing API
                        tld = self.get_tld_from_domain(result.domain)
                        pricing = self.get_pricing_for_tld(tld) if tld else {'registration': 0, 'renewal': 0, 'currency': 'USD'}

                        if pricing['registration'] > 0:
                            reg_price = f"${pricing['registration']:.2f}"
                            renew_price = f"${pricing['renewal']:.2f}"
                            currency = pricing['currency']
                        else:
                            reg_price = "N/A"
                            renew_price = "N/A"
                            currency = "USD"

                        tag = 'available'
                        status_icon = "✅"
                else:
                    unavailable_count += 1
                    reg_price = "N/A"
                    renew_price = "N/A"
                    currency = "N/A"
                    tag = 'unavailable'
                    status_icon = "❌"

                # Insert result với styling
                item = self.results_tree.insert("", tk.END, values=(
                    result.domain, f"{status_icon} {status}", domain_type, reg_price, renew_price, currency
                ), tags=(tag,))

        # Update status với thống kê
        total = len(results)
        self.status_var.set(f"✅ Hoàn thành: {total} domain | "
                           f"Có thể mua: {available_count} | "
                           f"Không khả dụng: {unavailable_count} | "
                           f"Lỗi: {error_count}")

        # Enable save button nếu có kết quả
        if results:
            self.save_button.config(state='normal')
    
    def show_error(self, error_msg: str):
        """Hiển thị lỗi"""
        messagebox.showerror("❌ Lỗi", error_msg)
        self.status_var.set("🔴 Lỗi khi kiểm tra domain")

    def finish_checking(self):
        """Kết thúc quá trình kiểm tra"""
        self.progress.stop()
        self.progress_label.config(text="Hoàn thành")
        self.check_button.config(state='normal')
        self.is_checking = False
    
    def run(self):
        """Chạy ứng dụng"""
        self.root.mainloop()


if __name__ == "__main__":
    app = DomainCheckerGUI()
    app.run()
