# 🌐 Namecheap Domain Checker

Ứng dụng Python để kiểm tra tính khả dụng (availability) và giá của domain names sử dụng Namecheap API.

## ✨ Tính năng

- ✅ Kiểm tra tính khả dụng của domain names
- 💰 Hiển thị giá đăng ký và gia hạn (cho premium domains)
- 🎯 Phân biệt domain thường và premium
- 🖥️ Giao diện đồ họa (GUI) thân thiện
- 💻 Giao diện dòng lệnh (CLI) linh hoạt
- 📁 Xử lý hàng loạt từ file
- 💾 Xuất kết quả ra file
- 🚨 Xử lý lỗi thông minh với thông báo tiếng Việt
- ⚡ Cache pricing để tối ưu hiệu suất

## 📋 Yêu cầu hệ thống

- **Python 3.7+**
- **Windows** (đã được tối ưu cho Windows)
- Kết nối Internet
- Tài khoản Namecheap với API access

## 🚀 Cài đặt

### 1. Clone hoặc tải về source code

```bash
git clone <repository-url>
cd san-domain
```

### 2. Cài đặt dependencies

```bash
pip install -r requirements.txt
```

### 3. Cấu hình API

1. Copy file cấu hình mẫu:
```bash
copy config_example.ini config.ini
```

2. Chỉnh sửa `config.ini` với thông tin API của bạn:
```ini
[API]
api_user = your_namecheap_username
api_key = your_api_key_here
username = your_namecheap_username
client_ip = your_server_ip_address
```

### 4. Lấy API credentials từ Namecheap

1. Đăng nhập vào [Namecheap](https://www.namecheap.com)
2. Vào **Profile** → **Tools** → **API Access**
3. Enable API access và lấy API Key
4. Whitelist IP address của bạn

## 🎯 Cách sử dụng

### GUI Mode (Giao diện đồ họa)

```bash
python main.py --gui
```

Hoặc chạy trực tiếp:
```bash
python gui_interface.py
```

**Tính năng GUI:**
- Nhập domain trực tiếp hoặc tải từ file
- Hiển thị kết quả dạng bảng với màu sắc
- Thanh tiến trình khi đang kiểm tra
- Lưu kết quả ra file
- Giao diện thân thiện, dễ sử dụng

### CLI Interactive Mode (Dòng lệnh tương tác)

```bash
python main.py
```

Hoặc:
```bash
python cli_interface.py
```

### CLI Batch Mode (Xử lý hàng loạt)

```bash
python main.py --file domains.txt
python main.py --file domains.txt --output results.txt
```

### Các lệnh hữu ích khác

```bash
# Test kết nối API
python main.py --test

# Tạo file domain mẫu
python main.py --sample

# Hiển thị hướng dẫn đầy đủ
python main.py --help-full

# Chạy test suite
python test_app.py
```

## 📝 Định dạng file input

Tạo file text với danh sách domain, mỗi domain một dòng:

```
# Danh sách domain cần kiểm tra
# Dòng bắt đầu bằng # sẽ bị bỏ qua

example.com
mydomain.net
awesome-site.org
premium-domain.xyz
test-domain-2024.info
```

## 📊 Kết quả đầu ra

### Console Output
```
✅ CÓ THỂ MUA example.com (THƯỜNG)
❌ KHÔNG KHẢ DỤNG mydomain.net
✅ CÓ THỂ MUA premium-domain.xyz (PREMIUM) - Giá: $1000.00 (gia hạn: $1000.00)
```

### File Output (TSV format)
```
Domain	Trạng thái	Loại	Giá đăng ký	Giá gia hạn	Tiền tệ	Ghi chú
example.com	Có thể mua	Thường	N/A	N/A	USD	
mydomain.net	Không khả dụng	Thường	N/A	N/A	USD	
premium-domain.xyz	Có thể mua	Premium	1000.00	1000.00	USD	
```

## 🔧 Cấu hình nâng cao

File `config.ini` hỗ trợ các tùy chọn:

```ini
[API]
api_user = your_username
api_key = your_api_key
username = your_username
client_ip = your_ip
api_url = https://api.namecheap.com/xml.response

[SETTINGS]
max_domains_per_request = 50    # Tối đa domain mỗi request
request_timeout = 30            # Timeout (giây)
cache_pricing = true            # Cache thông tin giá
cache_duration = 3600           # Thời gian cache (giây)
```

## 🚨 Xử lý lỗi

Ứng dụng xử lý thông minh các lỗi phổ biến:

- **Lỗi cấu hình API**: Hướng dẫn cách sửa
- **Lỗi kết nối**: Đề xuất thử lại
- **Lỗi giới hạn**: Tự động chia nhỏ request
- **Lỗi tài khoản**: Hướng dẫn liên hệ support

## 📁 Cấu trúc dự án

```
san-domain/
├── main.py                 # Module chính
├── namecheap_api.py        # API client
├── error_handler.py        # Xử lý lỗi
├── gui_interface.py        # Giao diện GUI
├── cli_interface.py        # Giao diện CLI
├── test_app.py            # Test suite
├── config.ini             # Cấu hình API (tạo từ example)
├── config_example.ini     # Mẫu cấu hình
├── requirements.txt       # Dependencies
└── README.md             # Tài liệu này
```

## 🧪 Testing

Chạy test suite:
```bash
python test_app.py
```

Test bao gồm:
- Unit tests cho tất cả modules
- Integration test với API thật
- Mock tests cho các trường hợp lỗi

## 🔗 API Reference

Ứng dụng sử dụng các Namecheap API endpoints:

- `namecheap.domains.check` - Kiểm tra availability
- `namecheap.users.getPricing` - Lấy thông tin giá

Tài liệu API: https://www.namecheap.com/support/api/

## ⚠️ Lưu ý quan trọng

1. **API Limits**: Namecheap giới hạn 50 domains per request
2. **IP Whitelist**: Phải whitelist IP trong Namecheap panel
3. **Rate Limiting**: Tránh gọi API quá thường xuyên
4. **Sandbox**: Sử dụng sandbox API cho testing
5. **Pricing**: Giá chỉ hiển thị cho premium domains

## 🆘 Troubleshooting

### Lỗi "Parameter APIUser is missing"
- Kiểm tra `api_user` trong config.ini
- Đảm bảo không có khoảng trắng thừa

### Lỗi "ClientIP is disabled or locked"
- Whitelist IP trong Namecheap panel
- Kiểm tra `client_ip` trong config.ini

### Lỗi "Unknown response from provider"
- Thử lại sau vài phút
- Kiểm tra kết nối internet

### GUI không hiển thị
- Đảm bảo đã cài tkinter: `pip install tk`
- Chạy trên Windows với GUI support

## 📞 Hỗ trợ

- **Namecheap API Support**: https://www.namecheap.com/support/
- **API Documentation**: https://www.namecheap.com/support/api/
- **Error Codes**: https://www.namecheap.com/support/api/error-codes/

## 📄 License

Dự án này được phát triển cho mục đích giáo dục và sử dụng cá nhân.

---

**Phát triển bởi**: AI Assistant  
**Ngày tạo**: 2025-01-25  
**Phiên bản**: 1.0.0
