"""
Domain Checker - Ứng dụng ch<PERSON>h
<PERSON>m tra tính khả dụng và giá của domain names sử dụng Namecheap API
"""

import sys
import os
import argparse
from typing import List
from namecheap_api import NamecheapAPI, NamecheapAPIError
from error_handler import <PERSON>rrorHandler
from cli_interface import DomainCheckerCLI
from gui_interface import DomainCheckerGUI


def check_config_file():
    """Kiểm tra file cấu hình"""
    if not os.path.exists('config.ini'):
        print("❌ Không tìm thấy file config.ini")
        print("📝 Tạo file config.ini từ config_example.ini và cập nhật thông tin API của bạn")
        
        if os.path.exists('config_example.ini'):
            print("\n💡 Bạn có thể copy config_example.ini thành config.ini:")
            print("   copy config_example.ini config.ini  (Windows)")
            print("   cp config_example.ini config.ini    (Linux/Mac)")
        
        return False
    return True


def test_api_connection():
    """Test kết nối API"""
    try:
        print("🔧 Đang test kết nối API...")
        api = NamecheapAPI()
        
        # Test với một domain đơn giản
        test_domains = ['test-domain-12345.com']
        results = api.check_domains(test_domains)
        
        if results:
            print("✅ Kết nối API thành công!")
            return True
        else:
            print("❌ Không nhận được kết quả từ API")
            return False
            
    except NamecheapAPIError as e:
        error_msg = ErrorHandler.format_error_message(e)
        print(f"❌ Lỗi API: {error_msg}")
        return False
    except Exception as e:
        print(f"❌ Lỗi không mong muốn: {str(e)}")
        return False


def create_sample_domain_file():
    """Tạo file domain mẫu để test"""
    sample_domains = [
        "# File domain mẫu để test",
        "# Mỗi dòng một domain, dòng bắt đầu bằng # sẽ bị bỏ qua",
        "",
        "example-test-domain-12345.com",
        "another-test-domain-67890.net",
        "sample-domain-test-999.org",
        "test-premium-domain.xyz",
        "my-awesome-domain-2024.info"
    ]
    
    try:
        with open('sample_domains.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(sample_domains))
        print("✅ Đã tạo file sample_domains.txt để test")
        return True
    except Exception as e:
        print(f"❌ Không thể tạo file mẫu: {str(e)}")
        return False


def print_help():
    """In hướng dẫn sử dụng"""
    print("""
🌐 NAMECHEAP DOMAIN CHECKER - HƯỚNG DẪN SỬ DỤNG

📋 CÁC CHẾ ĐỘ CHẠY:

1. GUI Mode (Giao diện đồ họa):
   python main.py --gui
   hoặc
   python gui_interface.py

2. CLI Interactive Mode (Giao diện dòng lệnh tương tác):
   python main.py
   hoặc
   python cli_interface.py

3. CLI Batch Mode (Xử lý hàng loạt):
   python main.py --file input.txt
   python main.py --file input.txt --output results.txt
   hoặc
   python cli_interface.py input.txt output.txt

4. Test API:
   python main.py --test

5. Tạo file domain mẫu:
   python main.py --sample

⚙️ CẤU HÌNH:

1. Copy file config_example.ini thành config.ini
2. Cập nhật thông tin API trong config.ini:
   - api_user: Username Namecheap của bạn
   - api_key: API key từ Namecheap
   - username: Username Namecheap của bạn
   - client_ip: IP address của server (có thể dùng 127.0.0.1 cho test)

📝 ĐỊNH DẠNG FILE INPUT:

- Mỗi domain một dòng
- Dòng bắt đầu bằng # sẽ bị bỏ qua (comment)
- Ví dụ:
  # Danh sách domain cần kiểm tra
  example.com
  mydomain.net
  awesome-site.org

🔗 LIÊN KẾT HỮU ÍCH:

- Namecheap API Documentation: https://www.namecheap.com/support/api/
- Đăng ký API Key: https://ap.www.namecheap.com/settings/tools/apiaccess/
""")


def main():
    """Hàm main"""
    parser = argparse.ArgumentParser(
        description="Namecheap Domain Availability Checker",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument('--gui', action='store_true', 
                       help='Chạy ở chế độ GUI')
    parser.add_argument('--file', type=str, 
                       help='File chứa danh sách domain để kiểm tra')
    parser.add_argument('--output', type=str, 
                       help='File output để lưu kết quả')
    parser.add_argument('--test', action='store_true', 
                       help='Test kết nối API')
    parser.add_argument('--sample', action='store_true', 
                       help='Tạo file domain mẫu')
    parser.add_argument('--help-full', action='store_true', 
                       help='Hiển thị hướng dẫn đầy đủ')
    
    args = parser.parse_args()
    
    # Hiển thị help đầy đủ
    if args.help_full:
        print_help()
        return
    
    # Tạo file mẫu
    if args.sample:
        create_sample_domain_file()
        return
    
    # Kiểm tra file cấu hình
    if not check_config_file():
        return
    
    # Test API
    if args.test:
        if test_api_connection():
            print("\n✅ API hoạt động bình thường. Bạn có thể sử dụng ứng dụng.")
        else:
            print("\n❌ Có vấn đề với API. Vui lòng kiểm tra cấu hình.")
        return
    
    try:
        # GUI mode
        if args.gui:
            print("🖥️ Khởi động GUI...")
            app = DomainCheckerGUI()
            app.run()
        
        # Batch mode
        elif args.file:
            print("📁 Chế độ batch processing...")
            cli = DomainCheckerCLI()
            cli.batch_mode(args.file, args.output)
        
        # Interactive CLI mode
        else:
            print("💻 Chế độ CLI tương tác...")
            cli = DomainCheckerCLI()
            cli.interactive_mode()
            
    except KeyboardInterrupt:
        print("\n\n👋 Tạm biệt!")
    except Exception as e:
        print(f"\n❌ Lỗi không mong muốn: {str(e)}")
        print("💡 Sử dụng --help-full để xem hướng dẫn chi tiết")


if __name__ == "__main__":
    main()
