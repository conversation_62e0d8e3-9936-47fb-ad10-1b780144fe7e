# 🚀 Hướng dẫn cài đặt nhanh

## Bước 1: Cài đặt Python dependencies

```bash
pip install -r requirements.txt
```

## Bước 2: Cấu hình API

1. **Copy file cấu hình:**
```bash
copy config_example.ini config.ini
```

2. **Chỉnh sửa config.ini:**
```ini
[API]
api_user = your_namecheap_username
api_key = your_api_key_here  
username = your_namecheap_username
client_ip = your_server_ip_address
```

## Bước 3: Lấy API credentials

1. Đăng nhập [Namecheap](https://www.namecheap.com)
2. Vào **Profile** → **Tools** → **API Access**
3. Enable API access
4. Copy API Key
5. Whitelist IP address

## Bước 4: Test ứng dụng

```bash
# Test API connection
python main.py --test

# Tạo file domain mẫu
python main.py --sample

# Chạy GUI
python main.py --gui

# Chạy CLI
python main.py
```

## 🎯 Sử dụng nhanh

### GUI (Giao diệ<PERSON> đ<PERSON> h<PERSON>a)
- Double-click `run_gui.bat`
- Hoặc: `python main.py --gui`

### CLI (Dòng lệnh)
- Double-click `run_cli.bat`  
- Hoặc: `python main.py`

### Batch processing
```bash
python main.py --file sample_domains.txt
```

## ⚠️ Lưu ý

- **API Key**: Phải có API access enabled
- **IP Whitelist**: Phải whitelist IP trong Namecheap panel
- **Giới hạn**: Tối đa 50 domains per request
- **Windows**: Ứng dụng được tối ưu cho Windows

## 🆘 Troubleshooting

| Lỗi | Giải pháp |
|-----|-----------|
| `APIUser is missing` | Kiểm tra `api_user` trong config.ini |
| `ClientIP is disabled` | Whitelist IP trong Namecheap panel |
| `GUI không hiển thị` | Cài tkinter: `pip install tk` |
| `Module not found` | Chạy: `pip install -r requirements.txt` |

## 📞 Hỗ trợ

- **API Docs**: https://www.namecheap.com/support/api/
- **API Access**: https://ap.www.namecheap.com/settings/tools/apiaccess/
